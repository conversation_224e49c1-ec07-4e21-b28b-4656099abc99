/** @format */

import {
	AuthenticatedAgentRequest,
	logAgentActivity,
	withAgentAuth,
} from '@/lib/agent-middleware';
import { awardCreditsWithoutTransaction } from '@/lib/credits';
import { db } from '@/lib/database';
import { logger } from '@/lib/logger';
import { NextResponse } from 'next/server';

// POST /api/agent/submissions/action - Process individual submission action
export const POST = withAgentAuth(['full_access'])(
	async (request: AuthenticatedAgentRequest) => {
		let submission_id: number | undefined;
		let action: string | undefined;

		try {
			const body = await request.json();
			({ submission_id, action } = body);
			const { notes } = body;

			// Validation
			if (!submission_id || !action) {
				return NextResponse.json(
					{ success: false, error: 'Submission ID and action are required' },
					{ status: 400 }
				);
			}

			const allowedActions = ['approve', 'reject', 'review', 'pending'];
			if (!allowedActions.includes(action)) {
				return NextResponse.json(
					{ success: false, error: 'Invalid action' },
					{ status: 400 }
				);
			}

			logger.info('Agent processing submission action', {
				agentId: request.agent.id,
				agentRole: request.agent.role,
				submissionId: submission_id,
				action,
				notes,
			});

			try {
				await db.query('BEGIN');

				// Map action to status
				const statusMap: { [key: string]: string } = {
					approve: 'approved',
					reject: 'rejected',
					review: 'reviewing',
					pending: 'pending',
				};

				const newStatus = statusMap[action];

				// Update the submission
				const updateQuery = `
        UPDATE spatial_schema.user_pois_temp
        SET
          admin_review_status = $1,
          admin_review_notes = $2,
          reviewed_by = $3,
          reviewed_at = NOW(),
          updated_at = NOW()
        WHERE id = $4
        RETURNING *
      `;

				const updateResult = await db.query(updateQuery, [
					newStatus,
					notes || null,
					request.agent.id,
					submission_id,
				]);

				if (updateResult.rows.length === 0) {
					throw new Error('Submission not found');
				}

				const submission = updateResult.rows[0];

				// Log submission details for debugging
				logger.info('Processing submission with details', {
					submissionId: submission_id,
					action,
					submissionReason: submission.submission_reason,
					submissionType: submission.submission_type,
					targetPoiId: submission.target_poi_id,
					originalPoiId: submission.original_poi_id,
					submittedByUserId: submission.submitted_by_user_id,
					name: submission.name,
					category: submission.category,
				});

				// Handle different actions
				if (action === 'approve') {
					let finalPoiId = null;

					// For new POI submissions, create entry in main POIs table first
					if (submission.submission_reason === 'new_poi') {
						const insertPoiQuery = `
            INSERT INTO spatial_schema.pois (
              name, name_en, name_tr, name_uk, name_de, name_ru, name_ar,
              category, subcategory, cuisine, city, district, neighborhood,
              street, full_address, province, phone_number, opening_hours,
              description, latitude, longitude, geom
            ) VALUES (
              $1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13,
              $14, $15, $16, $17, $18, $19, $20, $21, ST_SetSRID(ST_Point($21, $20), 4326)
            ) RETURNING id
          `;

						const poiResult = await db.query(insertPoiQuery, [
							submission.name,
							submission.name_en,
							submission.name_tr,
							submission.name_uk,
							submission.name_de,
							submission.name_ru,
							submission.name_ar,
							submission.category,
							submission.subcategory,
							submission.cuisine,
							submission.city,
							submission.district,
							submission.neighborhood,
							submission.street,
							submission.full_address,
							submission.province,
							submission.phone_number,
							submission.opening_hours,
							submission.description,
							submission.latitude,
							submission.longitude,
						]);

						finalPoiId = poiResult.rows[0].id;

						logger.info('New POI created in main table', {
							tempPoiId: submission.id,
							newPoiId: finalPoiId,
							agentId: request.agent.id,
						});
					} else if (
						submission.submission_reason === 'info_update' ||
						submission.submission_reason === 'closed'
					) {
						// For updates and closures, use the target POI ID
						finalPoiId = submission.target_poi_id;
					}

					// Insert into approved table for tracking
					const approveQuery = `
          INSERT INTO spatial_schema.user_pois_approved (
            original_temp_id,
            submitted_by_user_id,
            approved_by_admin_id,
            final_poi_id,
            name,
            name_en,
            name_tr,
            name_uk,
            name_de,
            name_ru,
            name_ar,
            category,
            subcategory,
            cuisine,
            city,
            district,
            neighborhood,
            street,
            full_address,
            province,
            phone_number,
            opening_hours,
            description,
            latitude,
            longitude,
            submission_notes,
            admin_review_notes,
            submission_type
          ) VALUES (
            $1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15, $16, $17, $18, $19, $20, $21, $22, $23, $24, $25, $26, $27, $28
          ) RETURNING id
        `;

					// Map submission_reason to submission_type for database constraint
					const submissionTypeMap: { [key: string]: string } = {
						closed: 'closure_request',
						info_update: 'info_update',
						new_poi: 'new_poi',
					};
					const submissionType =
						submissionTypeMap[submission.submission_reason] || 'new_poi';

					logger.info('About to insert into user_pois_approved', {
						submissionId: submission.id,
						submittedByUserId: submission.submitted_by_user_id,
						approvedByAdminId: request.agent.id,
						finalPoiId,
						submissionType,
					});

					const approveResult = await db.query(approveQuery, [
						submission.id,
						submission.submitted_by_user_id,
						request.agent.id,
						finalPoiId,
						submission.name,
						submission.name_en,
						submission.name_tr,
						submission.name_uk,
						submission.name_de,
						submission.name_ru,
						submission.name_ar,
						submission.category,
						submission.subcategory,
						submission.cuisine,
						submission.city,
						submission.district,
						submission.neighborhood,
						submission.street,
						submission.full_address,
						submission.province,
						submission.phone_number,
						submission.opening_hours,
						submission.description,
						submission.latitude,
						submission.longitude,
						submission.submission_notes,
						notes,
						submissionType,
					]);

					// Update the geom field separately using explicit values
					await db.query(
						`
          UPDATE spatial_schema.user_pois_approved
          SET geom = ST_SetSRID(ST_Point($1, $2), 4326)
          WHERE id = $3
        `,
						[
							submission.longitude,
							submission.latitude,
							approveResult.rows[0].id,
						]
					);

					// If this is an info update or removal, update the main POI as well
					if (
						submission.submission_reason === 'info_update' ||
						submission.submission_reason === 'closed'
					) {
						// Use target_poi_id to reference the original POI
						const targetPoiId = submission.target_poi_id;
						if (targetPoiId) {
							if (submission.submission_reason === 'info_update') {
								// Build update query for all editable fields (excluding ratings and counts)
								const editableFields = [
									'name',
									'name_en',
									'name_tr',
									'name_uk',
									'name_de',
									'name_ru',
									'name_ar',
									'category',
									'subcategory',
									'cuisine',
									'full_address',
									'street',
									'neighborhood',
									'district',
									'city',
									'province',
									'phone_number',
									'opening_hours',
									'description',
									'latitude',
									'longitude',
								];
								const sets = [];
								const params = [];
								let idx = 1;
								for (const field of editableFields) {
									if (
										submission[field] !== undefined &&
										submission[field] !== null
									) {
										sets.push(`${field} = $${idx++}`);
										params.push(submission[field]);
									}
								}
								if (sets.length > 0) {
									// Update the main POI (preserving ratings, counts, and other metrics)
									params.push(targetPoiId);
									await db.query(
										`UPDATE spatial_schema.pois SET ${sets.join(
											', '
										)}, updated_at = NOW() WHERE id = $${idx}`,
										params
									);
									logger.info(
										`Updated POI ${targetPoiId} with ${sets.length} fields`
									);
								}
							} else if (submission.submission_reason === 'closed') {
								// For closure: Special workflow to avoid FK constraint issues
								// 1. Get original POI data first
								const originalPoiResult = await db.query(
									`SELECT * FROM spatial_schema.pois WHERE id = $1`,
									[targetPoiId]
								);

								if (originalPoiResult.rows.length > 0) {
									const originalPoi = originalPoiResult.rows[0];

									// 2. Create approved record FIRST (while temp submission still exists for FK reference)
									await db.query(
										`
                  INSERT INTO spatial_schema.user_pois_approved (
                    original_temp_id, submitted_by_user_id, approved_by_admin_id,
                    final_poi_id, name, category, subcategory, city, district,
                    latitude, longitude, submission_notes, admin_review_notes, submission_type
                  ) VALUES (
                    $1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14
                  )
                `,
										[
											submission.id,
											submission.submitted_by_user_id,
											request.agent.id,
											null, // final_poi_id = NULL (no FK reference to deleted POI)
											originalPoi.name || 'Deleted POI',
											originalPoi.category || 'unknown',
											originalPoi.subcategory || 'unknown',
											originalPoi.city || 'unknown',
											originalPoi.district || 'unknown',
											originalPoi.latitude || 0,
											originalPoi.longitude || 0,
											submission.submission_notes || 'POI closure request',
											notes || 'POI approved for deletion',
											'closure_request',
										]
									);

									// 3. Hard delete the POI from main table
									await db.query(
										`DELETE FROM spatial_schema.pois WHERE id = $1`,
										[targetPoiId]
									);
									logger.info(
										`Hard deleted POI ${targetPoiId} after preserving data in approved table`
									);

									// 4.5. Award credits for approved POI deletion request - BEFORE deleting temp record
									if (submission.submitted_by_user_id) {
										// Import credit configuration
										const { creditConfig, creditReasons, creditDescriptions } =
											await import('@/lib/credits/creditConfig');

										logger.info(
											'Awarding credits for approved POI deletion request',
											{
												userId: submission.submitted_by_user_id,
												submissionId: submission.id,
												poiName: originalPoi.name,
												creditAmount: creditConfig.poi.delete,
											}
										);

										const creditAwarded = await awardCreditsWithoutTransaction(
											submission.submitted_by_user_id,
											creditConfig.poi.delete,
											creditReasons.poi.delete,
											creditDescriptions.poi.delete(
												originalPoi.name || 'Deleted POI'
											),
											'poi_submission',
											submission.id.toString(),
											false // Don't skip duplicate check
										);

										if (!creditAwarded) {
											logger.warn(
												'Failed to award credits for POI deletion approval',
												{
													userId: submission.submitted_by_user_id,
													submissionId: submission.id,
												}
											);
											// Don't fail the approval if credit awarding fails
										} else {
											logger.info(
												'Credits awarded successfully for POI deletion approval',
												{
													userId: submission.submitted_by_user_id,
													submissionId: submission.id,
													amount: creditConfig.poi.delete,
												}
											);
										}
									}

									// 4. Remove submission from temp table LAST (after approved record is created and credits awarded)
									// This must be done AFTER creating the approved record to avoid FK constraint violation
									await db.query(
										`DELETE FROM spatial_schema.user_pois_temp WHERE id = $1`,
										[submission.id]
									);

									// 5. Commit transaction
									await db.query('COMMIT');

									// 6. Log the agent activity
									await logAgentActivity(
										request.agent.id,
										'poi_submission_approve',
										'poi_submission',
										submission_id.toString(),
										{
											action: 'approve',
											newStatus: 'approved',
											notes,
											poiDeleted: targetPoiId,
										},
										request
									);

									logger.info('POI closure approved and deleted successfully', {
										submissionId: submission_id,
										deletedPoiId: targetPoiId,
										agentId: request.agent.id,
									});

									// 7. Return success response
									return NextResponse.json({
										success: true,
										message: 'POI closure approved and deleted successfully',
										submission: {
											id: submission.id,
											status: 'approved',
											reviewed_by: request.agent.id,
											reviewed_at: new Date().toISOString(),
											notes,
										},
									});
								} else {
									logger.warn(`POI ${targetPoiId} not found for deletion`);
									throw new Error(`POI ${targetPoiId} not found for deletion`);
								}
							}
						} else {
							logger.warn(
								`No target_poi_id found for ${submission.submission_reason} submission ${submission.id}`
							);
							throw new Error(
								`No target_poi_id found for ${submission.submission_reason} submission ${submission.id}`
							);
						}
					}
				} else if (action === 'reject') {
					// For reject, we remove the submission from temp table entirely
					// ✅ Clean 3-table system: No backend tracking table needed
					// Just delete from temp table
					await db.query(
						`
          DELETE FROM spatial_schema.user_pois_temp
          WHERE id = $1
        `,
						[submission_id]
					);

					await db.query('COMMIT');

					// Log the agent activity
					await logAgentActivity(
						request.agent.id,
						'poi_submission_reject',
						'poi_submission',
						submission_id.toString(),
						{ action: 'reject', notes },
						request
					);

					logger.info('Submission rejected and removed', {
						submissionId: submission_id,
						agentId: request.agent.id,
					});

					return NextResponse.json({
						success: true,
						message: 'Submission rejected and removed successfully',
						submission: {
							id: submission_id,
							status: 'rejected',
							reviewed_by: request.agent.id,
							reviewed_at: new Date().toISOString(),
							notes,
						},
					});
				}

				// Award credits for approved POI submissions - BEFORE commit
				if (action === 'approve' && submission.submitted_by_user_id) {
					// Import credit configuration
					const { creditConfig, creditReasons, creditDescriptions } =
						await import('@/lib/credits/creditConfig');

					// Determine credit amount and reason based on submission type
					let creditAmount: number;
					let creditReason: string;
					let creditDescription: string;

					if (submission.submission_reason === 'new_poi') {
						creditAmount = creditConfig.poi.add;
						creditReason = creditReasons.poi.add;
						creditDescription = creditDescriptions.poi.add(
							submission.name || 'Unnamed POI'
						);
					} else if (submission.submission_reason === 'info_update') {
						creditAmount = creditConfig.poi.update;
						creditReason = creditReasons.poi.update;
						creditDescription = creditDescriptions.poi.update(
							submission.name || 'Unnamed POI'
						);
					} else {
						// Default to new POI for backward compatibility
						creditAmount = creditConfig.poi.add;
						creditReason = creditReasons.poi.add;
						creditDescription = creditDescriptions.poi.add(
							submission.name || 'Unnamed POI'
						);
					}

					logger.info(
						'Awarding credits for approved POI submission via action route',
						{
							userId: submission.submitted_by_user_id,
							submissionId: submission.id,
							poiName: submission.name,
							submissionReason: submission.submission_reason,
							creditAmount,
						}
					);

					const creditAwarded = await awardCreditsWithoutTransaction(
						submission.submitted_by_user_id,
						creditAmount,
						creditReason,
						creditDescription,
						'poi_submission',
						submission.id.toString(),
						false // Don't skip duplicate check
					);

					if (!creditAwarded) {
						logger.warn(
							'Failed to award credits for POI approval via action route',
							{
								userId: submission.submitted_by_user_id,
								submissionId: submission.id,
							}
						);
						// Don't fail the approval if credit awarding fails
					} else {
						logger.info(
							'Credits awarded successfully for POI approval via action route',
							{
								userId: submission.submitted_by_user_id,
								submissionId: submission.id,
								amount: creditAmount,
							}
						);
					}
				}

				// ✅ Clean 3-table system: No backend tracking table needed
				// All tracking is done in user_pois_approved table
				// Note: For closure requests, transaction is already committed and response sent above
				if (action !== 'reject' && submission.submission_reason !== 'closed') {
					await db.query('COMMIT');

					// Log the agent activity
					await logAgentActivity(
						request.agent.id,
						`poi_submission_${action}`,
						'poi_submission',
						submission_id.toString(),
						{ action, newStatus, notes },
						request
					);

					logger.info('Submission action processed successfully', {
						submissionId: submission_id,
						action,
						newStatus,
						agentId: request.agent.id,
					});

					return NextResponse.json({
						success: true,
						message: `Submission ${action}d successfully`,
						submission: {
							id: submission.id,
							status: newStatus,
							reviewed_by: request.agent.id,
							reviewed_at: new Date().toISOString(),
							notes,
						},
					});
				}

				// This should never be reached, but added for TypeScript
				return NextResponse.json(
					{ success: false, error: 'Invalid action processed' },
					{ status: 400 }
				);
			} catch (dbError) {
				await db.query('ROLLBACK');

				// Handle database constraint errors
				if (
					typeof dbError === 'object' &&
					dbError !== null &&
					'code' in dbError
				) {
					const err = dbError as { code?: string; message?: string };
					if (err.code === '23503') {
						// Foreign key constraint violation
						logger.error(
							'Foreign key constraint violation during submission approval',
							{
								submissionId: submission_id,
								action,
								error: err.message,
								code: err.code,
							}
						);
						return NextResponse.json(
							{
								success: false,
								error: 'Invalid reference - related data not found',
							},
							{ status: 400 }
						);
					}
					if (err.code === '23502') {
						// Not null constraint violation
						logger.error('Required field missing during submission approval', {
							submissionId: submission_id,
							action,
							error: err.message,
							code: err.code,
						});
						return NextResponse.json(
							{ success: false, error: 'Required field missing' },
							{ status: 400 }
						);
					}
				}

				throw dbError;
			}
		} catch (error) {
			logger.error('Error processing submission action', {
				error,
				submissionId: submission_id,
				action: action,
				errorMessage: error instanceof Error ? error.message : String(error),
				errorStack: error instanceof Error ? error.stack : undefined,
			});
			return NextResponse.json(
				{ success: false, error: 'Failed to process action' },
				{ status: 500 }
			);
		}
	}
);
