/** @format */

import { Pool } from 'pg';
import { config } from './config';
import { handleDatabaseError } from './errors';
import { logger } from './logger';
import { monitorDatabaseQuery, performanceMetrics } from './performance';

// Database configuration with production-ready settings
const dbConfig: Record<string, unknown> = {
	user: config.database.user,
	host: config.database.host,
	database: config.database.name,
	password: config.database.password,
	port: config.database.port,
	max: config.database.maxConnections,
	idleTimeoutMillis: config.database.idleTimeoutMillis,
	connectionTimeoutMillis: 10000,
	statement_timeout: 30000,
	query_timeout: 30000,
};

// Only add SSL configuration if it's explicitly enabled
if (config.database.ssl) {
	dbConfig.ssl = config.database.ssl;
}

// Module-level singleton pattern
let poolInstance: Pool | null = null;
let shutdownHandlersRegistered = false;

function createPool(): Pool {
	if (poolInstance) {
		return poolInstance;
	}

	poolInstance = new Pool(dbConfig);

	// Enhanced connection event handlers
	poolInstance.on('connect', () => {
		logger.info('Database client connected', {
			totalCount: poolInstance!.totalCount,
			idleCount: poolInstance!.idleCount,
			waitingCount: poolInstance!.waitingCount,
		});
	});

	poolInstance.on('error', (err) => {
		logger.error('Database pool error', {
			error: err.message,
			stack: err.stack,
		});
	});

	poolInstance.on('remove', () => {
		logger.debug('Database client removed from pool');
	});

	// Register shutdown handlers only once
	if (!shutdownHandlersRegistered) {
		shutdownHandlersRegistered = true;
		let isShuttingDown = false;

		const gracefulShutdown = async (signal: string) => {
			if (isShuttingDown) {
				return;
			}

			isShuttingDown = true;
			console.log(`Received ${signal}, closing database pool...`);

			try {
				if (poolInstance) {
					await poolInstance.end();
					poolInstance = null;
				}
				console.log('Database pool closed successfully');
			} catch (error) {
				console.error('Error closing database pool:', error);
			}

			process.exit(0);
		};

		// Remove existing handlers before adding new ones to prevent duplicates
		process.removeAllListeners('SIGINT');
		process.removeAllListeners('SIGTERM');

		process.on('SIGINT', () => gracefulShutdown('SIGINT'));
		process.on('SIGTERM', () => gracefulShutdown('SIGTERM'));
	}

	return poolInstance;
}

// Export the pool instance
export const pool = createPool();

// Enhanced connection test with monitoring
export async function testConnection() {
	return monitorDatabaseQuery('connection_test', async () => {
		try {
			const client = await pool.connect();
			const result = await client.query(
				'SELECT NOW() as current_time, version() as db_version'
			);
			client.release();

			logger.info('Database connected successfully', {
				currentTime: result.rows[0].current_time,
				version: result.rows[0].db_version,
				poolStats: {
					totalCount: pool.totalCount,
					idleCount: pool.idleCount,
					waitingCount: pool.waitingCount,
				},
			});

			return true;
		} catch (error) {
			const dbError = handleDatabaseError(error);
			logger.error('Database connection failed', {
				error: dbError.message,
				code: dbError.code,
			});
			return false;
		}
	});
}

// Enhanced database helper functions with monitoring and error handling
export const db = {
	// Execute a query with monitoring
	async query(text: string, params?: unknown[]) {
		return monitorDatabaseQuery(
			'query',
			async () => {
				const client = await pool.connect();
				try {
					const result = await client.query(text, params);
					performanceMetrics.record('DB Query Duration', Date.now());
					return result;
				} catch (error) {
					// Log raw PostgreSQL error before transformation
					if (error && typeof error === 'object' && 'code' in error) {
						logger.error('Raw PostgreSQL error before transformation', {
							code: (error as any).code,
							message: (error as any).message,
							detail: (error as any).detail,
							hint: (error as any).hint,
							position: (error as any).position,
							constraint: (error as any).constraint,
							table: (error as any).table,
							column: (error as any).column,
							severity: (error as any).severity,
							query: text,
							params: params,
						});
					}
					throw handleDatabaseError(error);
				} finally {
					client.release();
				}
			},
			{ queryType: 'generic', paramCount: params?.length || 0 }
		);
	},

	// Execute multiple queries in a transaction
	async transaction(queries: Array<{ text: string; params?: unknown[] }>) {
		const client = await pool.connect();
		try {
			await client.query('BEGIN');
			const results = [];

			for (const query of queries) {
				const result = await client.query(query.text, query.params);
				results.push(result);
			}

			await client.query('COMMIT');
			return results;
		} catch (error) {
			await client.query('ROLLBACK');
			throw error;
		} finally {
			client.release();
		}
	},

	// Get a single row
	async getOne(text: string, params?: unknown[]) {
		const result = await this.query(text, params);
		return result.rows[0] || null;
	},

	// Get multiple rows
	async getMany(text: string, params?: unknown[]) {
		const result = await this.query(text, params);
		return result.rows;
	},

	// Insert and return the inserted row
	async insert(table: string, data: Record<string, unknown>) {
		const keys = Object.keys(data);
		const values = Object.values(data);
		const placeholders = keys.map((_, i) => `$${i + 1}`).join(', ');

		const query = `
      INSERT INTO ${table} (${keys.join(', ')})
      VALUES (${placeholders})
      RETURNING *
    `;

		const result = await this.query(query, values);
		return result.rows[0];
	},

	// Update and return the updated row
	async update(table: string, id: string, data: Record<string, unknown>) {
		const keys = Object.keys(data);
		const values = Object.values(data);
		const setClause = keys.map((key, i) => `${key} = $${i + 2}`).join(', ');

		const query = `
      UPDATE ${table}
      SET ${setClause}, updated_at = NOW()
      WHERE id = $1
      RETURNING *
    `;

		const result = await this.query(query, [id, ...values]);
		return result.rows[0];
	},

	// Delete a row
	async delete(table: string, id: string) {
		const query = `DELETE FROM ${table} WHERE id = $1 RETURNING *`;
		const result = await this.query(query, [id]);
		return result.rows[0];
	},
};

export const DB_SCHEMA = process.env.DB_SCHEMA || 'backend_schema';
export function table(name: string) {
	return `${DB_SCHEMA}.${name}`;
}
